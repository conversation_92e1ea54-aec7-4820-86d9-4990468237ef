package com.api.workflow.yytex.Invoice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.Map;

/**
 * OCR识别结果实体类 - 单个票据的识别结果
 */
public class OcrResult {
    
    /** 票据类型 */
    @JsonProperty("type")
    private String type;
    
    /** 宽度 */
    @JsonProperty("width")
    private Integer width;
    
    /** 识别概率 */
    @JsonProperty("probability")
    private BigDecimal probability;
    
    /** 高度 */
    @JsonProperty("height")
    private Integer height;
    
    /** 左边距 */
    @JsonProperty("left")
    private Integer left;
    
    /** 上边距 */
    @JsonProperty("top")
    private Integer top;
    
    /** 识别结果详情 */
    @JsonProperty("result")
    private Map<String, Object> result;

    // 构造函数
    public OcrResult() {}

    public OcrResult(String type, Map<String, Object> result) {
        this.type = type;
        this.result = result;
    }

    // Getter和Setter方法
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public BigDecimal getProbability() {
        return probability;
    }

    public void setProbability(BigDecimal probability) {
        this.probability = probability;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getLeft() {
        return left;
    }

    public void setLeft(Integer left) {
        this.left = left;
    }

    public Integer getTop() {
        return top;
    }

    public void setTop(Integer top) {
        this.top = top;
    }

    public Map<String, Object> getResult() {
        return result;
    }

    public void setResult(Map<String, Object> result) {
        this.result = result;
    }

    @Override
    public String toString() {
        return "OcrResult{" +
                "type='" + type + '\'' +
                ", width=" + width +
                ", probability=" + probability +
                ", height=" + height +
                ", left=" + left +
                ", top=" + top +
                ", result=" + result +
                '}';
    }
}
