package com.api.workflow.yytex.Invoice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 出租车票据OCR识别结果实体类
 */
public class TaxiReceiptOcr {
    
    /** 上车时间 */
    @JsonProperty("PickupTime")
    private List<OcrField> pickupTime;
    
    /** 下车时间 */
    @JsonProperty("DropoffTime")
    private List<OcrField> dropoffTime;
    
    /** 时间段 */
    @JsonProperty("Time")
    private List<OcrField> time;
    
    /** 城市 */
    @JsonProperty("City")
    private List<OcrField> city;
    
    /** 燃油附加费 */
    @JsonProperty("FuelOilSurcharge")
    private List<OcrField> fuelOilSurcharge;
    
    /** 日期 */
    @JsonProperty("Date")
    private List<OcrField> date;
    
    /** 省份 */
    @JsonProperty("Province")
    private List<OcrField> province;
    
    /** 叫车服务费 */
    @JsonProperty("CallServiceSurcharge")
    private List<OcrField> callServiceSurcharge;
    
    /** 车费 */
    @JsonProperty("Fare")
    private List<OcrField> fare;
    
    /** 总费用 */
    @JsonProperty("TotalFare")
    private List<OcrField> totalFare;
    
    /** 出租车号 */
    @JsonProperty("TaxiNum")
    private List<OcrField> taxiNum;
    
    /** 每公里价格 */
    @JsonProperty("PricePerkm")
    private List<OcrField> pricePerKm;
    
    /** 发票代码 */
    @JsonProperty("InvoiceCode")
    private List<OcrField> invoiceCode;
    
    /** 距离 */
    @JsonProperty("Distance")
    private List<OcrField> distance;
    
    /** 发票号码 */
    @JsonProperty("InvoiceNum")
    private List<OcrField> invoiceNum;
    
    /** 地点 */
    @JsonProperty("Location")
    private List<OcrField> location;

    // 构造函数
    public TaxiReceiptOcr() {}

    // Getter和Setter方法
    public List<OcrField> getPickupTime() {
        return pickupTime;
    }

    public void setPickupTime(List<OcrField> pickupTime) {
        this.pickupTime = pickupTime;
    }

    public List<OcrField> getDropoffTime() {
        return dropoffTime;
    }

    public void setDropoffTime(List<OcrField> dropoffTime) {
        this.dropoffTime = dropoffTime;
    }

    public List<OcrField> getTime() {
        return time;
    }

    public void setTime(List<OcrField> time) {
        this.time = time;
    }

    public List<OcrField> getCity() {
        return city;
    }

    public void setCity(List<OcrField> city) {
        this.city = city;
    }

    public List<OcrField> getFuelOilSurcharge() {
        return fuelOilSurcharge;
    }

    public void setFuelOilSurcharge(List<OcrField> fuelOilSurcharge) {
        this.fuelOilSurcharge = fuelOilSurcharge;
    }

    public List<OcrField> getDate() {
        return date;
    }

    public void setDate(List<OcrField> date) {
        this.date = date;
    }

    public List<OcrField> getProvince() {
        return province;
    }

    public void setProvince(List<OcrField> province) {
        this.province = province;
    }

    public List<OcrField> getCallServiceSurcharge() {
        return callServiceSurcharge;
    }

    public void setCallServiceSurcharge(List<OcrField> callServiceSurcharge) {
        this.callServiceSurcharge = callServiceSurcharge;
    }

    public List<OcrField> getFare() {
        return fare;
    }

    public void setFare(List<OcrField> fare) {
        this.fare = fare;
    }

    public List<OcrField> getTotalFare() {
        return totalFare;
    }

    public void setTotalFare(List<OcrField> totalFare) {
        this.totalFare = totalFare;
    }

    public List<OcrField> getTaxiNum() {
        return taxiNum;
    }

    public void setTaxiNum(List<OcrField> taxiNum) {
        this.taxiNum = taxiNum;
    }

    public List<OcrField> getPricePerKm() {
        return pricePerKm;
    }

    public void setPricePerKm(List<OcrField> pricePerKm) {
        this.pricePerKm = pricePerKm;
    }

    public List<OcrField> getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(List<OcrField> invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public List<OcrField> getDistance() {
        return distance;
    }

    public void setDistance(List<OcrField> distance) {
        this.distance = distance;
    }

    public List<OcrField> getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(List<OcrField> invoiceNum) {
        this.invoiceNum = invoiceNum;
    }

    public List<OcrField> getLocation() {
        return location;
    }

    public void setLocation(List<OcrField> location) {
        this.location = location;
    }

    @Override
    public String toString() {
        return "TaxiReceiptOcr{" +
                "pickupTime=" + pickupTime +
                ", dropoffTime=" + dropoffTime +
                ", totalFare=" + totalFare +
                ", taxiNum=" + taxiNum +
                ", invoiceNum=" + invoiceNum +
                ", date=" + date +
                ", location=" + location +
                '}';
    }
}
