package com.api.workflow.yytex.Invoice.service;

import com.api.workflow.yytex.Invoice.entity.*;
import com.api.workflow.yytex.Invoice.util.OcrEntityConverter;
import com.api.workflow.yytex.Invoice.util.OcrEntityConverter.VatInvoiceInfo;
import com.api.workflow.yytex.Invoice.util.OcrEntityConverter.TaxiReceiptInfo;
import org.json.JSONObject;
import org.json.JSONArray;
import org.json.JSONException;

import java.util.ArrayList;
import java.util.List;

/**
 * 基于实体类的发票服务 - 演示如何使用实体类处理OCR结果
 */
public class EntityBasedInvoiceService {
    
    /**
     * 使用实体类处理OCR识别结果
     */
    public JSONObject processOcrResultWithEntities(String ocrResponseJson) throws JSONException {
        JSONObject response = new JSONObject();
        
        try {
            // 1. 解析OCR响应为实体对象
            OcrResponse ocrResponse = OcrEntityConverter.parseOcrResponse(ocrResponseJson);
            
            // 2. 分类处理不同类型的票据
            List<VatInvoiceInfo> vatInvoices = new ArrayList<>();
            List<TaxiReceiptInfo> taxiReceipts = new ArrayList<>();
            
            for (OcrResult ocrResult : ocrResponse.getWordsResult()) {
                String type = ocrResult.getType();
                
                switch (type) {
                    case "vat_invoice":
                        VatInvoiceOcr vatOcr = OcrEntityConverter.convertToVatInvoiceOcr(ocrResult.getResult());
                        VatInvoiceInfo vatInfo = OcrEntityConverter.extractVatInvoiceInfo(vatOcr);
                        vatInvoices.add(vatInfo);
                        break;
                        
                    case "taxi_receipt":
                        TaxiReceiptOcr taxiOcr = OcrEntityConverter.convertToTaxiReceiptOcr(ocrResult.getResult());
                        TaxiReceiptInfo taxiInfo = OcrEntityConverter.extractTaxiReceiptInfo(taxiOcr);
                        taxiReceipts.add(taxiInfo);
                        break;
                        
                    default:
                        // 处理其他类型的票据
                        break;
                }
            }
            
            // 3. 构建响应结果
            response.put("success", true);
            response.put("totalResults", ocrResponse.getWordsResultNum());
            response.put("logId", ocrResponse.getLogId());
            
            // 4. 添加增值税发票信息
            JSONArray vatArray = new JSONArray();
            for (VatInvoiceInfo vatInfo : vatInvoices) {
                JSONObject vatJson = convertVatInfoToJson(vatInfo);
                vatArray.put(vatJson);
            }
            response.put("vatInvoices", vatArray);
            
            // 5. 添加出租车票据信息
            JSONArray taxiArray = new JSONArray();
            for (TaxiReceiptInfo taxiInfo : taxiReceipts) {
                JSONObject taxiJson = convertTaxiInfoToJson(taxiInfo);
                taxiArray.put(taxiJson);
            }
            response.put("taxiReceipts", taxiArray);
            
            // 6. 计算总金额
            double totalVatAmount = calculateTotalVatAmount(vatInvoices);
            double totalTaxiAmount = calculateTotalTaxiAmount(taxiReceipts);
            
            response.put("totalVatAmount", totalVatAmount);
            response.put("totalTaxiAmount", totalTaxiAmount);
            response.put("grandTotal", totalVatAmount + totalTaxiAmount);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "处理OCR结果失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 将增值税发票信息转换为JSON对象
     */
    private JSONObject convertVatInfoToJson(VatInvoiceInfo vatInfo) throws JSONException {
        JSONObject json = new JSONObject();
        json.put("ticketType", "vat_invoice");
        json.put("invoiceNum", vatInfo.getInvoiceNum());
        json.put("invoiceCode", vatInfo.getInvoiceCode());
        json.put("invoiceDate", vatInfo.getInvoiceDate());
        json.put("sellerName", vatInfo.getSellerName());
        json.put("purchaserName", vatInfo.getPurchaserName());
        json.put("purchaserTaxNum", vatInfo.getPurchaserTaxNum());
        json.put("amount", vatInfo.getAmountInFiguers());
        json.put("totalTax", vatInfo.getTotalTax());
        json.put("commodityNames", vatInfo.getCommodityNames());
        json.put("invoiceType", vatInfo.getInvoiceType());
        json.put("checker", vatInfo.getChecker());
        json.put("payee", vatInfo.getPayee());
        json.put("drawer", vatInfo.getDrawer());
        return json;
    }
    
    /**
     * 将出租车票据信息转换为JSON对象
     */
    private JSONObject convertTaxiInfoToJson(TaxiReceiptInfo taxiInfo) throws JSONException {
        JSONObject json = new JSONObject();
        json.put("ticketType", "taxi_receipt");
        json.put("invoiceNum", taxiInfo.getInvoiceNum());
        json.put("invoiceCode", taxiInfo.getInvoiceCode());
        json.put("date", taxiInfo.getDate());
        json.put("pickupTime", taxiInfo.getPickupTime());
        json.put("dropoffTime", taxiInfo.getDropoffTime());
        json.put("totalFare", taxiInfo.getTotalFare());
        json.put("taxiNum", taxiInfo.getTaxiNum());
        json.put("distance", taxiInfo.getDistance());
        json.put("location", taxiInfo.getLocation());
        json.put("province", taxiInfo.getProvince());
        json.put("city", taxiInfo.getCity());
        json.put("fare", taxiInfo.getFare());
        json.put("fuelOilSurcharge", taxiInfo.getFuelOilSurcharge());
        
        // 计算行程时间
        if (taxiInfo.getPickupTime() != null && taxiInfo.getDropoffTime() != null) {
            json.put("journeyTime", taxiInfo.getPickupTime() + "-" + taxiInfo.getDropoffTime());
        }
        
        return json;
    }
    
    /**
     * 计算增值税发票总金额
     */
    private double calculateTotalVatAmount(List<VatInvoiceInfo> vatInvoices) {
        double total = 0.0;
        for (VatInvoiceInfo vatInfo : vatInvoices) {
            try {
                String amountStr = vatInfo.getAmountInFiguers();
                if (amountStr != null && !amountStr.trim().isEmpty()) {
                    // 移除非数字字符
                    String numericValue = amountStr.replaceAll("[^0-9.]", "");
                    if (!numericValue.isEmpty()) {
                        total += Double.parseDouble(numericValue);
                    }
                }
            } catch (NumberFormatException e) {
                // 忽略无法解析的金额
            }
        }
        return total;
    }
    
    /**
     * 计算出租车票据总金额
     */
    private double calculateTotalTaxiAmount(List<TaxiReceiptInfo> taxiReceipts) {
        double total = 0.0;
        for (TaxiReceiptInfo taxiInfo : taxiReceipts) {
            try {
                String amountStr = taxiInfo.getTotalFare();
                if (amountStr != null && !amountStr.trim().isEmpty()) {
                    // 移除非数字字符
                    String numericValue = amountStr.replaceAll("[^0-9.]", "");
                    if (!numericValue.isEmpty()) {
                        total += Double.parseDouble(numericValue);
                    }
                }
            } catch (NumberFormatException e) {
                // 忽略无法解析的金额
            }
        }
        return total;
    }
    
    /**
     * 验证发票信息完整性
     */
    public boolean validateVatInvoice(VatInvoiceInfo vatInfo) {
        return vatInfo.getInvoiceNum() != null && !vatInfo.getInvoiceNum().trim().isEmpty() &&
               vatInfo.getAmountInFiguers() != null && !vatInfo.getAmountInFiguers().trim().isEmpty() &&
               vatInfo.getSellerName() != null && !vatInfo.getSellerName().trim().isEmpty();
    }
    
    /**
     * 验证出租车票据信息完整性
     */
    public boolean validateTaxiReceipt(TaxiReceiptInfo taxiInfo) {
        return taxiInfo.getInvoiceNum() != null && !taxiInfo.getInvoiceNum().trim().isEmpty() &&
               taxiInfo.getTotalFare() != null && !taxiInfo.getTotalFare().trim().isEmpty() &&
               taxiInfo.getDate() != null && !taxiInfo.getDate().trim().isEmpty();
    }
    
    /**
     * 获取发票重复检查所需的发票号列表
     */
    public List<String> extractInvoiceNumbers(List<VatInvoiceInfo> vatInvoices, List<TaxiReceiptInfo> taxiReceipts) {
        List<String> invoiceNumbers = new ArrayList<>();
        
        // 添加增值税发票号
        for (VatInvoiceInfo vatInfo : vatInvoices) {
            if (vatInfo.getInvoiceNum() != null && !vatInfo.getInvoiceNum().trim().isEmpty()) {
                invoiceNumbers.add(vatInfo.getInvoiceNum());
            }
        }
        
        // 添加出租车票据号
        for (TaxiReceiptInfo taxiInfo : taxiReceipts) {
            if (taxiInfo.getInvoiceNum() != null && !taxiInfo.getInvoiceNum().trim().isEmpty()) {
                invoiceNumbers.add(taxiInfo.getInvoiceNum());
            }
        }
        
        return invoiceNumbers;
    }
}
