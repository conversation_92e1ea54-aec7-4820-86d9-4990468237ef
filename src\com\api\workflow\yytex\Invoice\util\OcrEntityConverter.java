package com.api.workflow.yytex.Invoice.util;

import com.api.workflow.yytex.Invoice.entity.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.type.TypeFactory;
import java.util.List;
import java.util.Map;

/**
 * OCR结果转换工具类 - 将OCR识别结果转换为实体对象
 */
public class OcrEntityConverter {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 将JSON字符串转换为OcrResponse对象
     */
    public static OcrResponse parseOcrResponse(String jsonString) {
        try {
            return objectMapper.readValue(jsonString, OcrResponse.class);
        } catch (Exception e) {
            throw new RuntimeException("解析OCR响应失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 将OcrResult转换为具体的票据实体对象
     */
    public static Object convertToSpecificEntity(OcrResult ocrResult) {
        String type = ocrResult.getType();
        Map<String, Object> result = ocrResult.getResult();
        
        switch (type) {
            case "vat_invoice":
                return convertToVatInvoiceOcr(result);
            case "taxi_receipt":
                return convertToTaxiReceiptOcr(result);
            // 可以继续添加其他票据类型的转换
            default:
                return result; // 返回原始Map
        }
    }
    
    /**
     * 转换为增值税发票OCR实体
     */
    public static VatInvoiceOcr convertToVatInvoiceOcr(Map<String, Object> resultMap) {
        try {
            String json = objectMapper.writeValueAsString(resultMap);
            return objectMapper.readValue(json, VatInvoiceOcr.class);
        } catch (Exception e) {
            throw new RuntimeException("转换增值税发票OCR实体失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 转换为出租车票据OCR实体
     */
    public static TaxiReceiptOcr convertToTaxiReceiptOcr(Map<String, Object> resultMap) {
        try {
            String json = objectMapper.writeValueAsString(resultMap);
            return objectMapper.readValue(json, TaxiReceiptOcr.class);
        } catch (Exception e) {
            throw new RuntimeException("转换出租车票据OCR实体失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从OCR字段列表中提取第一个值
     */
    public static String extractFirstValue(List<OcrField> fields) {
        if (fields != null && !fields.isEmpty() && fields.get(0) != null) {
            return fields.get(0).getWord();
        }
        return "";
    }
    
    /**
     * 从OCR字段列表中提取所有值并用逗号连接
     */
    public static String extractAllValues(List<OcrField> fields) {
        if (fields == null || fields.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < fields.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            if (fields.get(i) != null && fields.get(i).getWord() != null) {
                sb.append(fields.get(i).getWord());
            }
        }
        return sb.toString();
    }
    
    /**
     * 从增值税发票OCR实体中提取关键信息
     */
    public static VatInvoiceInfo extractVatInvoiceInfo(VatInvoiceOcr vatOcr) {
        VatInvoiceInfo info = new VatInvoiceInfo();
        
        info.setInvoiceNum(extractFirstValue(vatOcr.getInvoiceNum()));
        info.setInvoiceCode(extractFirstValue(vatOcr.getInvoiceCode()));
        info.setInvoiceDate(extractFirstValue(vatOcr.getInvoiceDate()));
        info.setSellerName(extractFirstValue(vatOcr.getSellerName()));
        info.setPurchaserName(extractFirstValue(vatOcr.getPurchaserName()));
        info.setPurchaserTaxNum(extractFirstValue(vatOcr.getPurchaserRegisterNum()));
        info.setAmountInFiguers(extractFirstValue(vatOcr.getAmountInFiguers()));
        info.setTotalTax(extractFirstValue(vatOcr.getTotalTax()));
        info.setCommodityNames(extractAllValues(vatOcr.getCommodityName()));
        info.setInvoiceType(extractFirstValue(vatOcr.getInvoiceType()));
        info.setChecker(extractFirstValue(vatOcr.getChecker()));
        info.setPayee(extractFirstValue(vatOcr.getPayee()));
        info.setDrawer(extractFirstValue(vatOcr.getNoteDrawer()));
        
        return info;
    }
    
    /**
     * 从出租车票据OCR实体中提取关键信息
     */
    public static TaxiReceiptInfo extractTaxiReceiptInfo(TaxiReceiptOcr taxiOcr) {
        TaxiReceiptInfo info = new TaxiReceiptInfo();
        
        info.setInvoiceNum(extractFirstValue(taxiOcr.getInvoiceNum()));
        info.setInvoiceCode(extractFirstValue(taxiOcr.getInvoiceCode()));
        info.setDate(extractFirstValue(taxiOcr.getDate()));
        info.setPickupTime(extractFirstValue(taxiOcr.getPickupTime()));
        info.setDropoffTime(extractFirstValue(taxiOcr.getDropoffTime()));
        info.setTotalFare(extractFirstValue(taxiOcr.getTotalFare()));
        info.setTaxiNum(extractFirstValue(taxiOcr.getTaxiNum()));
        info.setDistance(extractFirstValue(taxiOcr.getDistance()));
        info.setLocation(extractFirstValue(taxiOcr.getLocation()));
        info.setProvince(extractFirstValue(taxiOcr.getProvince()));
        info.setCity(extractFirstValue(taxiOcr.getCity()));
        info.setFare(extractFirstValue(taxiOcr.getFare()));
        info.setFuelOilSurcharge(extractFirstValue(taxiOcr.getFuelOilSurcharge()));
        
        return info;
    }
    
    /**
     * 增值税发票信息简化类
     */
    public static class VatInvoiceInfo {
        private String invoiceNum;
        private String invoiceCode;
        private String invoiceDate;
        private String sellerName;
        private String purchaserName;
        private String purchaserTaxNum;
        private String amountInFiguers;
        private String totalTax;
        private String commodityNames;
        private String invoiceType;
        private String checker;
        private String payee;
        private String drawer;
        
        // Getter和Setter方法
        public String getInvoiceNum() { return invoiceNum; }
        public void setInvoiceNum(String invoiceNum) { this.invoiceNum = invoiceNum; }
        
        public String getInvoiceCode() { return invoiceCode; }
        public void setInvoiceCode(String invoiceCode) { this.invoiceCode = invoiceCode; }
        
        public String getInvoiceDate() { return invoiceDate; }
        public void setInvoiceDate(String invoiceDate) { this.invoiceDate = invoiceDate; }
        
        public String getSellerName() { return sellerName; }
        public void setSellerName(String sellerName) { this.sellerName = sellerName; }
        
        public String getPurchaserName() { return purchaserName; }
        public void setPurchaserName(String purchaserName) { this.purchaserName = purchaserName; }
        
        public String getPurchaserTaxNum() { return purchaserTaxNum; }
        public void setPurchaserTaxNum(String purchaserTaxNum) { this.purchaserTaxNum = purchaserTaxNum; }
        
        public String getAmountInFiguers() { return amountInFiguers; }
        public void setAmountInFiguers(String amountInFiguers) { this.amountInFiguers = amountInFiguers; }
        
        public String getTotalTax() { return totalTax; }
        public void setTotalTax(String totalTax) { this.totalTax = totalTax; }
        
        public String getCommodityNames() { return commodityNames; }
        public void setCommodityNames(String commodityNames) { this.commodityNames = commodityNames; }
        
        public String getInvoiceType() { return invoiceType; }
        public void setInvoiceType(String invoiceType) { this.invoiceType = invoiceType; }
        
        public String getChecker() { return checker; }
        public void setChecker(String checker) { this.checker = checker; }
        
        public String getPayee() { return payee; }
        public void setPayee(String payee) { this.payee = payee; }
        
        public String getDrawer() { return drawer; }
        public void setDrawer(String drawer) { this.drawer = drawer; }
    }
    
    /**
     * 出租车票据信息简化类
     */
    public static class TaxiReceiptInfo {
        private String invoiceNum;
        private String invoiceCode;
        private String date;
        private String pickupTime;
        private String dropoffTime;
        private String totalFare;
        private String taxiNum;
        private String distance;
        private String location;
        private String province;
        private String city;
        private String fare;
        private String fuelOilSurcharge;
        
        // Getter和Setter方法
        public String getInvoiceNum() { return invoiceNum; }
        public void setInvoiceNum(String invoiceNum) { this.invoiceNum = invoiceNum; }
        
        public String getInvoiceCode() { return invoiceCode; }
        public void setInvoiceCode(String invoiceCode) { this.invoiceCode = invoiceCode; }
        
        public String getDate() { return date; }
        public void setDate(String date) { this.date = date; }
        
        public String getPickupTime() { return pickupTime; }
        public void setPickupTime(String pickupTime) { this.pickupTime = pickupTime; }
        
        public String getDropoffTime() { return dropoffTime; }
        public void setDropoffTime(String dropoffTime) { this.dropoffTime = dropoffTime; }
        
        public String getTotalFare() { return totalFare; }
        public void setTotalFare(String totalFare) { this.totalFare = totalFare; }
        
        public String getTaxiNum() { return taxiNum; }
        public void setTaxiNum(String taxiNum) { this.taxiNum = taxiNum; }
        
        public String getDistance() { return distance; }
        public void setDistance(String distance) { this.distance = distance; }
        
        public String getLocation() { return location; }
        public void setLocation(String location) { this.location = location; }
        
        public String getProvince() { return province; }
        public void setProvince(String province) { this.province = province; }
        
        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }
        
        public String getFare() { return fare; }
        public void setFare(String fare) { this.fare = fare; }
        
        public String getFuelOilSurcharge() { return fuelOilSurcharge; }
        public void setFuelOilSurcharge(String fuelOilSurcharge) { this.fuelOilSurcharge = fuelOilSurcharge; }
    }
}
