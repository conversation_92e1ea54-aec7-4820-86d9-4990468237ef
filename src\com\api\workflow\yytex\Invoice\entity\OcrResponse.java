package com.api.workflow.yytex.Invoice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * OCR识别响应实体类 - 顶层响应结构
 */
public class OcrResponse {
    
    /** 识别结果列表 */
    @JsonProperty("words_result")
    private List<OcrResult> wordsResult;
    
    /** 识别结果数量 */
    @JsonProperty("words_result_num")
    private Integer wordsResultNum;
    
    /** 日志ID */
    @JsonProperty("log_id")
    private Long logId;

    // 构造函数
    public OcrResponse() {}

    public OcrResponse(List<OcrResult> wordsResult, Integer wordsResultNum, Long logId) {
        this.wordsResult = wordsResult;
        this.wordsResultNum = wordsResultNum;
        this.logId = logId;
    }

    // Getter和Setter方法
    public List<OcrResult> getWordsResult() {
        return wordsResult;
    }

    public void setWordsResult(List<OcrResult> wordsResult) {
        this.wordsResult = wordsResult;
    }

    public Integer getWordsResultNum() {
        return wordsResultNum;
    }

    public void setWordsResultNum(Integer wordsResultNum) {
        this.wordsResultNum = wordsResultNum;
    }

    public Long getLogId() {
        return logId;
    }

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    @Override
    public String toString() {
        return "OcrResponse{" +
                "wordsResult=" + wordsResult +
                ", wordsResultNum=" + wordsResultNum +
                ", logId=" + logId +
                '}';
    }
}
