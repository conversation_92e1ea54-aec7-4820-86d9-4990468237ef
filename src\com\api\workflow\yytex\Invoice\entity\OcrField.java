package com.api.workflow.yytex.Invoice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * OCR字段实体类 - 表示识别出的单个字段
 */
public class OcrField {
    
    /** 识别的文字内容 */
    @JsonProperty("word")
    private String word;
    
    /** 行号（用于表格类数据） */
    @JsonProperty("row")
    private String row;
    
    /** 列号（用于表格类数据） */
    @JsonProperty("col")
    private String col;

    // 构造函数
    public OcrField() {}

    public OcrField(String word) {
        this.word = word;
    }

    public OcrField(String word, String row) {
        this.word = word;
        this.row = row;
    }

    // Getter和Setter方法
    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }

    public String getRow() {
        return row;
    }

    public void setRow(String row) {
        this.row = row;
    }

    public String getCol() {
        return col;
    }

    public void setCol(String col) {
        this.col = col;
    }

    @Override
    public String toString() {
        return "OcrField{" +
                "word='" + word + '\'' +
                ", row='" + row + '\'' +
                ", col='" + col + '\'' +
                '}';
    }
}
