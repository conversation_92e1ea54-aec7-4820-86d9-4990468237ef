package com.api.workflow.yytex.Invoice.example;

import com.api.workflow.yytex.Invoice.entity.*;
import com.api.workflow.yytex.Invoice.service.EntityBasedInvoiceService;
import com.api.workflow.yytex.Invoice.util.OcrEntityConverter;
import com.api.workflow.yytex.Invoice.util.OcrEntityConverter.VatInvoiceInfo;
import com.api.workflow.yytex.Invoice.util.OcrEntityConverter.TaxiReceiptInfo;
import org.json.JSONObject;

/**
 * 实体类使用示例 - 演示如何使用实体类处理OCR结果
 */
public class EntityUsageExample {
    
    public static void main(String[] args) {
        // 示例OCR响应JSON（基于您提供的数据）
        String ocrResponseJson = "{\n" +
                "    \"words_result\": [\n" +
                "        {\n" +
                "            \"type\": \"vat_invoice\",\n" +
                "            \"width\": 0,\n" +
                "            \"probability\": 0.9980429411,\n" +
                "            \"height\": 649,\n" +
                "            \"left\": 154,\n" +
                "            \"top\": 177,\n" +
                "            \"result\": {\n" +
                "                \"AmountInWords\": [{\"word\": \"叁佰陆拾圆整\"}],\n" +
                "                \"InvoiceNum\": [{\"word\": \"07286261\"}],\n" +
                "                \"AmountInFiguers\": [{\"word\": \"360.00\"}],\n" +
                "                \"SellerName\": [{\"word\": \"百度智能云\"}],\n" +
                "                \"PurchaserName\": [{\"word\": \"百度在线网络技术(北京)有限公司\"}],\n" +
                "                \"InvoiceDate\": [{\"word\": \"2019年08月28日\"}],\n" +
                "                \"CommodityName\": [{\"row\": \"1\", \"word\": \"*信息技术服务*软件服务费\"}]\n" +
                "            }\n" +
                "        },\n" +
                "        {\n" +
                "            \"type\": \"taxi_receipt\",\n" +
                "            \"width\": 0,\n" +
                "            \"probability\": 0.9858493805,\n" +
                "            \"height\": 615,\n" +
                "            \"left\": 1325,\n" +
                "            \"top\": 200,\n" +
                "            \"result\": {\n" +
                "                \"PickupTime\": [{\"word\": \"10:50\"}],\n" +
                "                \"DropoffTime\": [{\"word\": \"17:06\"}],\n" +
                "                \"TotalFare\": [{\"word\": \"22.00\"}],\n" +
                "                \"TaxiNum\": [{\"word\": \"AQ6353\"}],\n" +
                "                \"InvoiceNum\": [{\"word\": \"05070716\"}],\n" +
                "                \"Date\": [{\"word\": \"2019-03-20\"}],\n" +
                "                \"Location\": [{\"word\": \"陕西省\"}]\n" +
                "            }\n" +
                "        }\n" +
                "    ],\n" +
                "    \"words_result_num\": 2,\n" +
                "    \"log_id\": 1438382953545048984\n" +
                "}";
        
        try {
            // 演示使用实体类处理OCR结果
            demonstrateEntityUsage(ocrResponseJson);
        } catch (Exception e) {
            System.err.println("处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 演示实体类的使用方法
     */
    public static void demonstrateEntityUsage(String ocrResponseJson) throws Exception {
        System.out.println("=== 实体类使用示例 ===\n");
        
        // 1. 解析OCR响应
        System.out.println("1. 解析OCR响应:");
        OcrResponse ocrResponse = OcrEntityConverter.parseOcrResponse(ocrResponseJson);
        System.out.println("识别结果数量: " + ocrResponse.getWordsResultNum());
        System.out.println("日志ID: " + ocrResponse.getLogId());
        System.out.println();
        
        // 2. 处理每个识别结果
        System.out.println("2. 处理识别结果:");
        for (int i = 0; i < ocrResponse.getWordsResult().size(); i++) {
            OcrResult result = ocrResponse.getWordsResult().get(i);
            System.out.println("结果 " + (i + 1) + ":");
            System.out.println("  类型: " + result.getType());
            System.out.println("  识别概率: " + result.getProbability());
            
            // 根据类型转换为具体实体
            if ("vat_invoice".equals(result.getType())) {
                processVatInvoice(result);
            } else if ("taxi_receipt".equals(result.getType())) {
                processTaxiReceipt(result);
            }
            System.out.println();
        }
        
        // 3. 使用服务类处理
        System.out.println("3. 使用服务类处理:");
        EntityBasedInvoiceService service = new EntityBasedInvoiceService();
        JSONObject processedResult = service.processOcrResultWithEntities(ocrResponseJson);
        System.out.println("处理结果: " + processedResult.toString(2));
    }
    
    /**
     * 处理增值税发票
     */
    private static void processVatInvoice(OcrResult result) {
        System.out.println("  === 增值税发票处理 ===");
        
        // 转换为增值税发票OCR实体
        VatInvoiceOcr vatOcr = OcrEntityConverter.convertToVatInvoiceOcr(result.getResult());
        
        // 提取关键信息
        VatInvoiceInfo vatInfo = OcrEntityConverter.extractVatInvoiceInfo(vatOcr);
        
        // 显示提取的信息
        System.out.println("  发票号码: " + vatInfo.getInvoiceNum());
        System.out.println("  发票代码: " + vatInfo.getInvoiceCode());
        System.out.println("  开票日期: " + vatInfo.getInvoiceDate());
        System.out.println("  销售方: " + vatInfo.getSellerName());
        System.out.println("  购买方: " + vatInfo.getPurchaserName());
        System.out.println("  金额: " + vatInfo.getAmountInFiguers());
        System.out.println("  税额: " + vatInfo.getTotalTax());
        System.out.println("  商品名称: " + vatInfo.getCommodityNames());
        
        // 验证信息完整性
        EntityBasedInvoiceService service = new EntityBasedInvoiceService();
        boolean isValid = service.validateVatInvoice(vatInfo);
        System.out.println("  信息完整性: " + (isValid ? "有效" : "无效"));
    }
    
    /**
     * 处理出租车票据
     */
    private static void processTaxiReceipt(OcrResult result) {
        System.out.println("  === 出租车票据处理 ===");
        
        // 转换为出租车票据OCR实体
        TaxiReceiptOcr taxiOcr = OcrEntityConverter.convertToTaxiReceiptOcr(result.getResult());
        
        // 提取关键信息
        TaxiReceiptInfo taxiInfo = OcrEntityConverter.extractTaxiReceiptInfo(taxiOcr);
        
        // 显示提取的信息
        System.out.println("  发票号码: " + taxiInfo.getInvoiceNum());
        System.out.println("  发票代码: " + taxiInfo.getInvoiceCode());
        System.out.println("  日期: " + taxiInfo.getDate());
        System.out.println("  上车时间: " + taxiInfo.getPickupTime());
        System.out.println("  下车时间: " + taxiInfo.getDropoffTime());
        System.out.println("  总费用: " + taxiInfo.getTotalFare());
        System.out.println("  出租车号: " + taxiInfo.getTaxiNum());
        System.out.println("  距离: " + taxiInfo.getDistance());
        System.out.println("  地点: " + taxiInfo.getLocation());
        
        // 验证信息完整性
        EntityBasedInvoiceService service = new EntityBasedInvoiceService();
        boolean isValid = service.validateTaxiReceipt(taxiInfo);
        System.out.println("  信息完整性: " + (isValid ? "有效" : "无效"));
    }
}
