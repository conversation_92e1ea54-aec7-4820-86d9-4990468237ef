package com.api.workflow.yytex.Invoice.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 发票基础实体类 - 所有发票类型的基类
 */
public abstract class BaseInvoice {
    
    /** 票据类型 */
    @JsonProperty("ticketType")
    private String ticketType;
    
    /** 发票号码 */
    @JsonProperty("invoiceNum")
    private String invoiceNum;
    
    /** 发票日期 */
    @JsonProperty("invoiceDate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date invoiceDate;
    
    /** 金额 */
    @JsonProperty("amount")
    private BigDecimal amount;
    
    /** 销售方名称 */
    @JsonProperty("sellerName")
    private String sellerName;
    
    /** 购买方名称 */
    @JsonProperty("purchaserName")
    private String purchaserName;
    
    /** 备注信息 */
    @JsonProperty("remarks")
    private String remarks;
    
    /** 原始识别数据 */
    @JsonProperty("rawData")
    private String rawData;

    // 构造函数
    public BaseInvoice() {}
    
    public BaseInvoice(String ticketType, String invoiceNum, BigDecimal amount) {
        this.ticketType = ticketType;
        this.invoiceNum = invoiceNum;
        this.amount = amount;
    }

    // Getter和Setter方法
    public String getTicketType() {
        return ticketType;
    }

    public void setTicketType(String ticketType) {
        this.ticketType = ticketType;
    }

    public String getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(String invoiceNum) {
        this.invoiceNum = invoiceNum;
    }

    public Date getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(Date invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getSellerName() {
        return sellerName;
    }

    public void setSellerName(String sellerName) {
        this.sellerName = sellerName;
    }

    public String getPurchaserName() {
        return purchaserName;
    }

    public void setPurchaserName(String purchaserName) {
        this.purchaserName = purchaserName;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getRawData() {
        return rawData;
    }

    public void setRawData(String rawData) {
        this.rawData = rawData;
    }

    @Override
    public String toString() {
        return "BaseInvoice{" +
                "ticketType='" + ticketType + '\'' +
                ", invoiceNum='" + invoiceNum + '\'' +
                ", invoiceDate=" + invoiceDate +
                ", amount=" + amount +
                ", sellerName='" + sellerName + '\'' +
                ", purchaserName='" + purchaserName + '\'' +
                '}';
    }
}
