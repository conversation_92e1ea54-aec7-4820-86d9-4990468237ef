package com.api.workflow.yytex.Invoice.entity;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;

/**
 * 增值税发票OCR识别结果实体类
 */
public class VatInvoiceOcr {
    
    /** 金额大写 */
    @JsonProperty("AmountInWords")
    private List<OcrField> amountInWords;
    
    /** 发票号码确认 */
    @JsonProperty("InvoiceNumConfirm")
    private List<OcrField> invoiceNumConfirm;
    
    /** 商品结束日期 */
    @JsonProperty("CommodityEndDate")
    private List<OcrField> commodityEndDate;
    
    /** 商品车辆类型 */
    @JsonProperty("CommodityVehicleType")
    private List<OcrField> commodityVehicleType;
    
    /** 商品开始日期 */
    @JsonProperty("CommodityStartDate")
    private List<OcrField> commodityStartDate;
    
    /** 商品单价 */
    @JsonProperty("CommodityPrice")
    private List<OcrField> commodityPrice;
    
    /** 开票人 */
    @JsonProperty("NoteDrawer")
    private List<OcrField> noteDrawer;
    
    /** 销售方地址 */
    @JsonProperty("SellerAddress")
    private List<OcrField> sellerAddress;
    
    /** 商品数量 */
    @JsonProperty("CommodityNum")
    private List<OcrField> commodityNum;
    
    /** 销售方税号 */
    @JsonProperty("SellerRegisterNum")
    private List<OcrField> sellerRegisterNum;
    
    /** 机器编码 */
    @JsonProperty("MachineCode")
    private List<OcrField> machineCode;
    
    /** 备注 */
    @JsonProperty("Remarks")
    private List<OcrField> remarks;
    
    /** 销售方银行账号 */
    @JsonProperty("SellerBank")
    private List<OcrField> sellerBank;
    
    /** 商品税率 */
    @JsonProperty("CommodityTaxRate")
    private List<OcrField> commodityTaxRate;
    
    /** 合计税额 */
    @JsonProperty("TotalTax")
    private List<OcrField> totalTax;
    
    /** 发票代码确认 */
    @JsonProperty("InvoiceCodeConfirm")
    private List<OcrField> invoiceCodeConfirm;
    
    /** 校验码 */
    @JsonProperty("CheckCode")
    private List<OcrField> checkCode;
    
    /** 发票代码 */
    @JsonProperty("InvoiceCode")
    private List<OcrField> invoiceCode;
    
    /** 开票日期 */
    @JsonProperty("InvoiceDate")
    private List<OcrField> invoiceDate;
    
    /** 购买方税号 */
    @JsonProperty("PurchaserRegisterNum")
    private List<OcrField> purchaserRegisterNum;
    
    /** 发票类型原始 */
    @JsonProperty("InvoiceTypeOrg")
    private List<OcrField> invoiceTypeOrg;
    
    /** 在线支付 */
    @JsonProperty("OnlinePay")
    private List<OcrField> onlinePay;
    
    /** 密码区 */
    @JsonProperty("Password")
    private List<OcrField> password;
    
    /** 代理标识 */
    @JsonProperty("Agent")
    private List<OcrField> agent;
    
    /** 金额小写 */
    @JsonProperty("AmountInFiguers")
    private List<OcrField> amountInFiguers;
    
    /** 购买方银行账号 */
    @JsonProperty("PurchaserBank")
    private List<OcrField> purchaserBank;
    
    /** 复核人 */
    @JsonProperty("Checker")
    private List<OcrField> checker;
    
    /** 城市 */
    @JsonProperty("City")
    private List<OcrField> city;
    
    /** 合计金额 */
    @JsonProperty("TotalAmount")
    private List<OcrField> totalAmount;
    
    /** 商品金额 */
    @JsonProperty("CommodityAmount")
    private List<OcrField> commodityAmount;
    
    /** 购买方名称 */
    @JsonProperty("PurchaserName")
    private List<OcrField> purchaserName;
    
    /** 商品类型 */
    @JsonProperty("CommodityType")
    private List<OcrField> commodityType;
    
    /** 省份 */
    @JsonProperty("Province")
    private List<OcrField> province;
    
    /** 发票类型 */
    @JsonProperty("InvoiceType")
    private List<OcrField> invoiceType;
    
    /** 联次 */
    @JsonProperty("SheetNum")
    private List<OcrField> sheetNum;
    
    /** 购买方地址 */
    @JsonProperty("PurchaserAddress")
    private List<OcrField> purchaserAddress;
    
    /** 商品税额 */
    @JsonProperty("CommodityTax")
    private List<OcrField> commodityTax;
    
    /** 商品车牌号 */
    @JsonProperty("CommodityPlateNum")
    private List<OcrField> commodityPlateNum;
    
    /** 商品单位 */
    @JsonProperty("CommodityUnit")
    private List<OcrField> commodityUnit;
    
    /** 收款人 */
    @JsonProperty("Payee")
    private List<OcrField> payee;
    
    /** 商品名称 */
    @JsonProperty("CommodityName")
    private List<OcrField> commodityName;
    
    /** 销售方名称 */
    @JsonProperty("SellerName")
    private List<OcrField> sellerName;
    
    /** 发票号码 */
    @JsonProperty("InvoiceNum")
    private List<OcrField> invoiceNum;

    // 构造函数
    public VatInvoiceOcr() {}

    // Getter和Setter方法
    public List<OcrField> getAmountInWords() {
        return amountInWords;
    }

    public void setAmountInWords(List<OcrField> amountInWords) {
        this.amountInWords = amountInWords;
    }

    public List<OcrField> getInvoiceNumConfirm() {
        return invoiceNumConfirm;
    }

    public void setInvoiceNumConfirm(List<OcrField> invoiceNumConfirm) {
        this.invoiceNumConfirm = invoiceNumConfirm;
    }

    public List<OcrField> getCommodityEndDate() {
        return commodityEndDate;
    }

    public void setCommodityEndDate(List<OcrField> commodityEndDate) {
        this.commodityEndDate = commodityEndDate;
    }

    public List<OcrField> getCommodityVehicleType() {
        return commodityVehicleType;
    }

    public void setCommodityVehicleType(List<OcrField> commodityVehicleType) {
        this.commodityVehicleType = commodityVehicleType;
    }

    public List<OcrField> getCommodityStartDate() {
        return commodityStartDate;
    }

    public void setCommodityStartDate(List<OcrField> commodityStartDate) {
        this.commodityStartDate = commodityStartDate;
    }

    public List<OcrField> getCommodityPrice() {
        return commodityPrice;
    }

    public void setCommodityPrice(List<OcrField> commodityPrice) {
        this.commodityPrice = commodityPrice;
    }

    public List<OcrField> getNoteDrawer() {
        return noteDrawer;
    }

    public void setNoteDrawer(List<OcrField> noteDrawer) {
        this.noteDrawer = noteDrawer;
    }

    public List<OcrField> getSellerAddress() {
        return sellerAddress;
    }

    public void setSellerAddress(List<OcrField> sellerAddress) {
        this.sellerAddress = sellerAddress;
    }

    public List<OcrField> getCommodityNum() {
        return commodityNum;
    }

    public void setCommodityNum(List<OcrField> commodityNum) {
        this.commodityNum = commodityNum;
    }

    public List<OcrField> getSellerRegisterNum() {
        return sellerRegisterNum;
    }

    public void setSellerRegisterNum(List<OcrField> sellerRegisterNum) {
        this.sellerRegisterNum = sellerRegisterNum;
    }

    public List<OcrField> getMachineCode() {
        return machineCode;
    }

    public void setMachineCode(List<OcrField> machineCode) {
        this.machineCode = machineCode;
    }

    public List<OcrField> getRemarks() {
        return remarks;
    }

    public void setRemarks(List<OcrField> remarks) {
        this.remarks = remarks;
    }

    public List<OcrField> getSellerBank() {
        return sellerBank;
    }

    public void setSellerBank(List<OcrField> sellerBank) {
        this.sellerBank = sellerBank;
    }

    public List<OcrField> getCommodityTaxRate() {
        return commodityTaxRate;
    }

    public void setCommodityTaxRate(List<OcrField> commodityTaxRate) {
        this.commodityTaxRate = commodityTaxRate;
    }

    public List<OcrField> getTotalTax() {
        return totalTax;
    }

    public void setTotalTax(List<OcrField> totalTax) {
        this.totalTax = totalTax;
    }

    public List<OcrField> getInvoiceCodeConfirm() {
        return invoiceCodeConfirm;
    }

    public void setInvoiceCodeConfirm(List<OcrField> invoiceCodeConfirm) {
        this.invoiceCodeConfirm = invoiceCodeConfirm;
    }

    public List<OcrField> getCheckCode() {
        return checkCode;
    }

    public void setCheckCode(List<OcrField> checkCode) {
        this.checkCode = checkCode;
    }

    public List<OcrField> getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(List<OcrField> invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public List<OcrField> getInvoiceDate() {
        return invoiceDate;
    }

    public void setInvoiceDate(List<OcrField> invoiceDate) {
        this.invoiceDate = invoiceDate;
    }

    public List<OcrField> getPurchaserRegisterNum() {
        return purchaserRegisterNum;
    }

    public void setPurchaserRegisterNum(List<OcrField> purchaserRegisterNum) {
        this.purchaserRegisterNum = purchaserRegisterNum;
    }

    public List<OcrField> getInvoiceTypeOrg() {
        return invoiceTypeOrg;
    }

    public void setInvoiceTypeOrg(List<OcrField> invoiceTypeOrg) {
        this.invoiceTypeOrg = invoiceTypeOrg;
    }

    public List<OcrField> getOnlinePay() {
        return onlinePay;
    }

    public void setOnlinePay(List<OcrField> onlinePay) {
        this.onlinePay = onlinePay;
    }

    public List<OcrField> getPassword() {
        return password;
    }

    public void setPassword(List<OcrField> password) {
        this.password = password;
    }

    public List<OcrField> getAgent() {
        return agent;
    }

    public void setAgent(List<OcrField> agent) {
        this.agent = agent;
    }

    public List<OcrField> getAmountInFiguers() {
        return amountInFiguers;
    }

    public void setAmountInFiguers(List<OcrField> amountInFiguers) {
        this.amountInFiguers = amountInFiguers;
    }

    public List<OcrField> getPurchaserBank() {
        return purchaserBank;
    }

    public void setPurchaserBank(List<OcrField> purchaserBank) {
        this.purchaserBank = purchaserBank;
    }

    public List<OcrField> getChecker() {
        return checker;
    }

    public void setChecker(List<OcrField> checker) {
        this.checker = checker;
    }

    public List<OcrField> getCity() {
        return city;
    }

    public void setCity(List<OcrField> city) {
        this.city = city;
    }

    public List<OcrField> getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(List<OcrField> totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<OcrField> getCommodityAmount() {
        return commodityAmount;
    }

    public void setCommodityAmount(List<OcrField> commodityAmount) {
        this.commodityAmount = commodityAmount;
    }

    public List<OcrField> getPurchaserName() {
        return purchaserName;
    }

    public void setPurchaserName(List<OcrField> purchaserName) {
        this.purchaserName = purchaserName;
    }

    public List<OcrField> getCommodityType() {
        return commodityType;
    }

    public void setCommodityType(List<OcrField> commodityType) {
        this.commodityType = commodityType;
    }

    public List<OcrField> getProvince() {
        return province;
    }

    public void setProvince(List<OcrField> province) {
        this.province = province;
    }

    public List<OcrField> getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(List<OcrField> invoiceType) {
        this.invoiceType = invoiceType;
    }

    public List<OcrField> getSheetNum() {
        return sheetNum;
    }

    public void setSheetNum(List<OcrField> sheetNum) {
        this.sheetNum = sheetNum;
    }

    public List<OcrField> getPurchaserAddress() {
        return purchaserAddress;
    }

    public void setPurchaserAddress(List<OcrField> purchaserAddress) {
        this.purchaserAddress = purchaserAddress;
    }

    public List<OcrField> getCommodityTax() {
        return commodityTax;
    }

    public void setCommodityTax(List<OcrField> commodityTax) {
        this.commodityTax = commodityTax;
    }

    public List<OcrField> getCommodityPlateNum() {
        return commodityPlateNum;
    }

    public void setCommodityPlateNum(List<OcrField> commodityPlateNum) {
        this.commodityPlateNum = commodityPlateNum;
    }

    public List<OcrField> getCommodityUnit() {
        return commodityUnit;
    }

    public void setCommodityUnit(List<OcrField> commodityUnit) {
        this.commodityUnit = commodityUnit;
    }

    public List<OcrField> getPayee() {
        return payee;
    }

    public void setPayee(List<OcrField> payee) {
        this.payee = payee;
    }

    public List<OcrField> getCommodityName() {
        return commodityName;
    }

    public void setCommodityName(List<OcrField> commodityName) {
        this.commodityName = commodityName;
    }

    public List<OcrField> getSellerName() {
        return sellerName;
    }

    public void setSellerName(List<OcrField> sellerName) {
        this.sellerName = sellerName;
    }

    public List<OcrField> getInvoiceNum() {
        return invoiceNum;
    }

    public void setInvoiceNum(List<OcrField> invoiceNum) {
        this.invoiceNum = invoiceNum;
    }

    @Override
    public String toString() {
        return "VatInvoiceOcr{" +
                "invoiceNum=" + invoiceNum +
                ", invoiceCode=" + invoiceCode +
                ", invoiceDate=" + invoiceDate +
                ", sellerName=" + sellerName +
                ", purchaserName=" + purchaserName +
                ", amountInFiguers=" + amountInFiguers +
                ", totalTax=" + totalTax +
                '}';
    }
}
